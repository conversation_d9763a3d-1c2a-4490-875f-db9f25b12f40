/**
 * 节点注册服务
 * 负责管理和注册可视化脚本节点
 */

import { EventEmitter } from 'events';

/**
 * 节点分类枚举
 */
export enum NodeCategory {
  EVENTS = 'events',
  FLOW = 'flow',
  LOGIC = 'logic',
  MATH = 'math',
  STRING = 'string',
  ARRAY = 'array',
  OBJECT = 'object',
  VARIABLE = 'variable',
  DEBUG = 'debug',
  ENTITY = 'entity',
  TRANSFORM = 'transform',
  PHYSICS = 'physics',
  ANIMATION = 'animation',
  AUDIO = 'audio',
  INPUT = 'input',
  UI = 'ui',
  RENDERING = 'rendering',
  NETWORK = 'network',
  AI = 'ai',
  TIME = 'time',
  CUSTOM = 'custom'
}

/**
 * 节点信息接口
 */
export interface NodeInfo {
  type: string;
  label: string;
  description: string;
  category: NodeCategory;
  icon: string;
  color: string;
  tags: string[];
  constructor?: any;
  metadata?: any;
}

/**
 * 节点注册服务类
 */
export class NodeRegistryService extends EventEmitter {
  private static instance: NodeRegistryService | null = null;
  private registeredNodes: Map<string, NodeInfo> = new Map();
  private nodesByCategory: Map<NodeCategory, NodeInfo[]> = new Map();
  private nodesByTag: Map<string, NodeInfo[]> = new Map();

  /**
   * 获取单例实例
   */
  public static getInstance(): NodeRegistryService {
    if (!NodeRegistryService.instance) {
      NodeRegistryService.instance = new NodeRegistryService();
    }
    return NodeRegistryService.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    super();
    this.initializeDefaultNodes();
    this.initializeBatch1Nodes();
    this.initializeBatch2Nodes();
    this.initializeBatch3Nodes();
    this.initializeBatch4Nodes();
    this.initializeBatch5Nodes();
    this.initializeBatch6Nodes();
    this.initializeBatch7Nodes();
    this.initializeBatch8Nodes();
  }

  /**
   * 初始化默认节点
   */
  private initializeDefaultNodes(): void {
    // 核心事件节点
    this.registerNode({
      type: 'core/events/onStart',
      label: '开始事件',
      description: '当视觉脚本开始执行时触发',
      category: NodeCategory.EVENTS,
      icon: 'play',
      color: '#52c41a',
      tags: ['事件', '生命周期']
    });

    this.registerNode({
      type: 'core/events/onUpdate',
      label: '更新事件',
      description: '每帧更新时触发',
      category: NodeCategory.EVENTS,
      icon: 'sync',
      color: '#1890ff',
      tags: ['事件', '生命周期']
    });

    // 调试节点
    this.registerNode({
      type: 'core/debug/print',
      label: '打印',
      description: '在控制台打印日志',
      category: NodeCategory.DEBUG,
      icon: 'console',
      color: '#722ed1',
      tags: ['调试', '输出']
    });

    // 数学节点
    this.registerNode({
      type: 'math/basic/add',
      label: '加法',
      description: '计算两个数的和',
      category: NodeCategory.MATH,
      icon: 'plus',
      color: '#fa8c16',
      tags: ['数学', '运算']
    });

    // 流程控制节点
    this.registerNode({
      type: 'core/flow/delay',
      label: '延迟',
      description: '延迟指定时间后执行',
      category: NodeCategory.FLOW,
      icon: 'clock',
      color: '#eb2f96',
      tags: ['流程', '时间']
    });
  }

  /**
   * 初始化第1批次节点：核心基础节点 (001-050)
   */
  private initializeBatch1Nodes(): void {
    // 核心事件节点 (003-005) - 001和002已在默认节点中注册
    this.registerNode({
      type: 'core/events/onEnd',
      label: '结束事件',
      description: '当视觉脚本结束时触发',
      category: NodeCategory.EVENTS,
      icon: 'stop',
      color: '#f5222d',
      tags: ['事件', '生命周期', '核心', '批次1']
    });

    this.registerNode({
      type: 'core/events/onPause',
      label: '暂停事件',
      description: '当视觉脚本暂停时触发',
      category: NodeCategory.EVENTS,
      icon: 'pause',
      color: '#fa8c16',
      tags: ['事件', '生命周期', '核心', '批次1']
    });

    this.registerNode({
      type: 'core/events/onResume',
      label: '恢复事件',
      description: '当视觉脚本恢复时触发',
      category: NodeCategory.EVENTS,
      icon: 'play-circle',
      color: '#52c41a',
      tags: ['事件', '生命周期', '核心', '批次1']
    });

    // 数学运算节点 (007-015) - 006已在默认节点中注册
    this.registerNode({
      type: 'math/basic/subtract',
      label: '减法',
      description: '计算两个数的差',
      category: NodeCategory.MATH,
      icon: 'minus',
      color: '#fa8c16',
      tags: ['数学', '运算', '基础', '批次1']
    });

    this.registerNode({
      type: 'math/basic/multiply',
      label: '乘法',
      description: '计算两个数的积',
      category: NodeCategory.MATH,
      icon: 'close',
      color: '#fa8c16',
      tags: ['数学', '运算', '基础', '批次1']
    });

    this.registerNode({
      type: 'math/basic/divide',
      label: '除法',
      description: '计算两个数的商',
      category: NodeCategory.MATH,
      icon: 'line',
      color: '#fa8c16',
      tags: ['数学', '运算', '基础', '批次1']
    });

    this.registerNode({
      type: 'math/trigonometry/sin',
      label: '正弦',
      description: '计算角度的正弦值',
      category: NodeCategory.MATH,
      icon: 'function',
      color: '#fa8c16',
      tags: ['数学', '三角函数', '正弦', '批次1']
    });

    this.registerNode({
      type: 'math/trigonometry/cos',
      label: '余弦',
      description: '计算角度的余弦值',
      category: NodeCategory.MATH,
      icon: 'function',
      color: '#fa8c16',
      tags: ['数学', '三角函数', '余弦', '批次1']
    });

    this.registerNode({
      type: 'math/vector/magnitude',
      label: '向量长度',
      description: '计算向量的长度',
      category: NodeCategory.MATH,
      icon: 'arrow-right',
      color: '#fa8c16',
      tags: ['数学', '向量', '长度', '批次1']
    });

    this.registerNode({
      type: 'math/vector/normalize',
      label: '向量归一化',
      description: '将向量归一化为单位向量',
      category: NodeCategory.MATH,
      icon: 'arrow-right',
      color: '#fa8c16',
      tags: ['数学', '向量', '归一化', '批次1']
    });

    this.registerNode({
      type: 'math/basic/multiply',
      label: '乘法',
      description: '计算两个数的积',
      category: NodeCategory.MATH,
      icon: 'close',
      color: '#fa8c16',
      tags: ['数学', '运算', '基础', '批次1']
    });

    this.registerNode({
      type: 'math/basic/divide',
      label: '除法',
      description: '计算两个数的商',
      category: NodeCategory.MATH,
      icon: 'line',
      color: '#fa8c16',
      tags: ['数学', '运算', '基础', '批次1']
    });

    this.registerNode({
      type: 'math/basic/modulo',
      label: '取模',
      description: '计算两个数的余数',
      category: NodeCategory.MATH,
      icon: 'percentage',
      color: '#fa8c16',
      tags: ['数学', '运算', '基础', '批次1']
    });

    // 逻辑比较节点 (016-025)
    this.registerNode({
      type: 'logic/comparison/equal',
      label: '等于',
      description: '比较两个值是否相等',
      category: NodeCategory.LOGIC,
      icon: 'equal',
      color: '#722ed1',
      tags: ['逻辑', '比较', '条件', '批次1']
    });

    this.registerNode({
      type: 'logic/comparison/notEqual',
      label: '不等于',
      description: '比较两个值是否不相等',
      category: NodeCategory.LOGIC,
      icon: 'not-equal',
      color: '#722ed1',
      tags: ['逻辑', '比较', '条件', '批次1']
    });

    this.registerNode({
      type: 'logic/comparison/greater',
      label: '大于',
      description: '比较第一个值是否大于第二个值',
      category: NodeCategory.LOGIC,
      icon: 'greater',
      color: '#722ed1',
      tags: ['逻辑', '比较', '条件', '批次1']
    });

    this.registerNode({
      type: 'logic/comparison/less',
      label: '小于',
      description: '比较第一个值是否小于第二个值',
      category: NodeCategory.LOGIC,
      icon: 'less',
      color: '#722ed1',
      tags: ['逻辑', '比较', '条件', '批次1']
    });

    this.registerNode({
      type: 'logic/boolean/and',
      label: '逻辑与',
      description: '逻辑与运算',
      category: NodeCategory.LOGIC,
      icon: 'and',
      color: '#722ed1',
      tags: ['逻辑', '布尔', '运算', '批次1']
    });

    // 流程控制节点 (026-035) - 028已在默认节点中注册
    this.registerNode({
      type: 'core/flow/branch',
      label: '分支',
      description: '根据条件选择执行路径',
      category: NodeCategory.FLOW,
      icon: 'branch',
      color: '#eb2f96',
      tags: ['流程', '控制', '分支', '批次1']
    });

    this.registerNode({
      type: 'core/flow/sequence',
      label: '序列',
      description: '按顺序执行多个操作',
      category: NodeCategory.FLOW,
      icon: 'sequence',
      color: '#eb2f96',
      tags: ['流程', '控制', '序列', '批次1']
    });

    // 实体操作节点 (036-045)
    this.registerNode({
      type: 'entity/create',
      label: '创建实体',
      description: '创建新的实体对象',
      category: NodeCategory.ENTITY,
      icon: 'plus-circle',
      color: '#13c2c2',
      tags: ['实体', '创建', '对象', '批次1']
    });

    this.registerNode({
      type: 'entity/destroy',
      label: '销毁实体',
      description: '销毁指定的实体对象',
      category: NodeCategory.ENTITY,
      icon: 'delete',
      color: '#13c2c2',
      tags: ['实体', '销毁', '对象', '批次1']
    });

    this.registerNode({
      type: 'entity/find',
      label: '查找实体',
      description: '根据条件查找实体',
      category: NodeCategory.ENTITY,
      icon: 'search',
      color: '#13c2c2',
      tags: ['实体', '查找', '搜索', '批次1']
    });

    this.registerNode({
      type: 'entity/component/add',
      label: '添加组件',
      description: '为实体添加组件',
      category: NodeCategory.ENTITY,
      icon: 'plus-square',
      color: '#13c2c2',
      tags: ['实体', '组件', '添加', '批次1']
    });

    this.registerNode({
      type: 'entity/component/remove',
      label: '移除组件',
      description: '从实体移除组件',
      category: NodeCategory.ENTITY,
      icon: 'minus-square',
      color: '#13c2c2',
      tags: ['实体', '组件', '移除', '批次1']
    });

    // 基础物理节点 (046-050)
    this.registerNode({
      type: 'physics/gravity/set',
      label: '设置重力',
      description: '设置物理世界的重力',
      category: NodeCategory.PHYSICS,
      icon: 'gravity',
      color: '#9c27b0',
      tags: ['物理', '重力', '世界', '批次1']
    });

    this.registerNode({
      type: 'physics/collision/detect',
      label: '碰撞检测',
      description: '检测两个物体是否碰撞',
      category: NodeCategory.PHYSICS,
      icon: 'collision',
      color: '#9c27b0',
      tags: ['物理', '碰撞', '检测', '批次1']
    });

    this.registerNode({
      type: 'physics/rigidbody/create',
      label: '创建刚体',
      description: '为实体创建刚体组件',
      category: NodeCategory.PHYSICS,
      icon: 'rigidbody',
      color: '#9c27b0',
      tags: ['物理', '刚体', '创建', '批次1']
    });

    this.registerNode({
      type: 'physics/force/apply',
      label: '施加力',
      description: '向刚体施加力',
      category: NodeCategory.PHYSICS,
      icon: 'force',
      color: '#9c27b0',
      tags: ['物理', '力', '施加', '批次1']
    });

    this.registerNode({
      type: 'physics/velocity/set',
      label: '设置速度',
      description: '设置刚体的速度',
      category: NodeCategory.PHYSICS,
      icon: 'velocity',
      color: '#9c27b0',
      tags: ['物理', '速度', '设置', '批次1']
    });

    // 添加缺失的实体节点
    this.registerNode({
      type: 'entity/get',
      label: '获取实体',
      description: '根据ID或名称获取实体',
      category: NodeCategory.ENTITY,
      icon: 'get',
      color: '#13c2c2',
      tags: ['实体', '获取', '查询', '批次1']
    });

    this.registerNode({
      type: 'entity/component/get',
      label: '获取组件',
      description: '从实体获取指定组件',
      category: NodeCategory.ENTITY,
      icon: 'component',
      color: '#13c2c2',
      tags: ['实体', '组件', '获取', '批次1']
    });

    this.registerNode({
      type: 'entity/transform/getPosition',
      label: '获取位置',
      description: '获取实体的位置',
      category: NodeCategory.ENTITY,
      icon: 'position',
      color: '#13c2c2',
      tags: ['实体', '变换', '位置', '批次1']
    });

    this.registerNode({
      type: 'entity/transform/setPosition',
      label: '设置位置',
      description: '设置实体的位置',
      category: NodeCategory.ENTITY,
      icon: 'position',
      color: '#13c2c2',
      tags: ['实体', '变换', '位置', '批次1']
    });

    this.registerNode({
      type: 'entity/transform/getRotation',
      label: '获取旋转',
      description: '获取实体的旋转',
      category: NodeCategory.ENTITY,
      icon: 'rotation',
      color: '#13c2c2',
      tags: ['实体', '变换', '旋转', '批次1']
    });

    this.registerNode({
      type: 'entity/transform/setRotation',
      label: '设置旋转',
      description: '设置实体的旋转',
      category: NodeCategory.ENTITY,
      icon: 'rotation',
      color: '#13c2c2',
      tags: ['实体', '变换', '旋转', '批次1']
    });

    // 添加缺失的物理节点
    this.registerNode({
      type: 'physics/raycast',
      label: '射线检测',
      description: '进行射线碰撞检测',
      category: NodeCategory.PHYSICS,
      icon: 'raycast',
      color: '#9c27b0',
      tags: ['物理', '射线', '检测', '批次1']
    });

    this.registerNode({
      type: 'physics/applyForce',
      label: '应用力',
      description: '向刚体应用力',
      category: NodeCategory.PHYSICS,
      icon: 'force',
      color: '#9c27b0',
      tags: ['物理', '力', '应用', '批次1']
    });

    this.registerNode({
      type: 'physics/applyImpulse',
      label: '应用冲量',
      description: '向刚体应用冲量',
      category: NodeCategory.PHYSICS,
      icon: 'impulse',
      color: '#9c27b0',
      tags: ['物理', '冲量', '应用', '批次1']
    });

    this.registerNode({
      type: 'physics/setVelocity',
      label: '设置速度',
      description: '设置刚体的速度',
      category: NodeCategory.PHYSICS,
      icon: 'velocity',
      color: '#9c27b0',
      tags: ['物理', '速度', '设置', '批次1']
    });

    this.registerNode({
      type: 'physics/getVelocity',
      label: '获取速度',
      description: '获取刚体的速度',
      category: NodeCategory.PHYSICS,
      icon: 'velocity',
      color: '#9c27b0',
      tags: ['物理', '速度', '获取', '批次1']
    });

    this.registerNode({
      type: 'physics/collision/onEnter',
      label: '碰撞进入',
      description: '检测碰撞进入事件',
      category: NodeCategory.PHYSICS,
      icon: 'collision',
      color: '#9c27b0',
      tags: ['物理', '碰撞', '事件', '批次1']
    });

    this.registerNode({
      type: 'physics/collision/onExit',
      label: '碰撞退出',
      description: '检测碰撞退出事件',
      category: NodeCategory.PHYSICS,
      icon: 'collision',
      color: '#9c27b0',
      tags: ['物理', '碰撞', '事件', '批次1']
    });

    // 添加软体物理节点
    this.registerNode({
      type: 'physics/softbody/createCloth',
      label: '创建布料',
      description: '创建软体布料物理对象',
      category: NodeCategory.PHYSICS,
      icon: 'cloth',
      color: '#9c27b0',
      tags: ['物理', '软体', '布料', '批次1']
    });

    this.registerNode({
      type: 'physics/softbody/createRope',
      label: '创建绳索',
      description: '创建软体绳索物理对象',
      category: NodeCategory.PHYSICS,
      icon: 'rope',
      color: '#9c27b0',
      tags: ['物理', '软体', '绳索', '批次1']
    });

    this.registerNode({
      type: 'physics/softbody/createSoftBody',
      label: '创建软体',
      description: '创建通用软体物理对象',
      category: NodeCategory.PHYSICS,
      icon: 'softbody',
      color: '#9c27b0',
      tags: ['物理', '软体', '创建', '批次1']
    });

    this.registerNode({
      type: 'physics/softbody/setStiffness',
      label: '设置刚度',
      description: '设置软体的刚度属性',
      category: NodeCategory.PHYSICS,
      icon: 'stiffness',
      color: '#9c27b0',
      tags: ['物理', '软体', '刚度', '批次1']
    });

    this.registerNode({
      type: 'physics/softbody/setDamping',
      label: '设置阻尼',
      description: '设置软体的阻尼属性',
      category: NodeCategory.PHYSICS,
      icon: 'damping',
      color: '#9c27b0',
      tags: ['物理', '软体', '阻尼', '批次1']
    });

    // 添加网络节点
    this.registerNode({
      type: 'network/connectToServer',
      label: '连接到服务器',
      description: '建立与服务器的网络连接',
      category: NodeCategory.NETWORK,
      icon: 'connect',
      color: '#52c41a',
      tags: ['网络', '连接', '服务器', '批次1']
    });

    this.registerNode({
      type: 'network/sendMessage',
      label: '发送网络消息',
      description: '向服务器发送消息',
      category: NodeCategory.NETWORK,
      icon: 'send',
      color: '#52c41a',
      tags: ['网络', '发送', '消息', '批次1']
    });

    this.registerNode({
      type: 'network/events/onMessage',
      label: '接收网络消息',
      description: '接收来自服务器的消息',
      category: NodeCategory.NETWORK,
      icon: 'receive',
      color: '#52c41a',
      tags: ['网络', '接收', '消息', '批次1']
    });

    this.registerNode({
      type: 'network/disconnect',
      label: '断开连接',
      description: '断开与服务器的网络连接',
      category: NodeCategory.NETWORK,
      icon: 'disconnect',
      color: '#52c41a',
      tags: ['网络', '断开', '连接', '批次1']
    });

    // 添加AI节点
    this.registerNode({
      type: 'ai/animation/generateBodyAnimation',
      label: '生成身体动画',
      description: '使用AI生成身体动画',
      category: NodeCategory.AI,
      icon: 'body-animation',
      color: '#f759ab',
      tags: ['AI', '动画', '身体', '批次1']
    });

    this.registerNode({
      type: 'ai/animation/generateFacialAnimation',
      label: '生成面部动画',
      description: '使用AI生成面部动画',
      category: NodeCategory.AI,
      icon: 'facial-animation',
      color: '#f759ab',
      tags: ['AI', '动画', '面部', '批次1']
    });

    this.registerNode({
      type: 'ai/model/load',
      label: '加载AI模型',
      description: '加载AI推理模型',
      category: NodeCategory.AI,
      icon: 'model',
      color: '#f759ab',
      tags: ['AI', '模型', '加载', '批次1']
    });

    this.registerNode({
      type: 'ai/model/generateText',
      label: '生成文本',
      description: '使用AI模型生成文本',
      category: NodeCategory.AI,
      icon: 'text-generation',
      color: '#f759ab',
      tags: ['AI', '文本', '生成', '批次1']
    });

    // 添加缺失的核心事件节点
    this.registerNode({
      type: 'core/events/onEnd',
      label: '结束事件',
      description: '当视觉脚本结束时触发',
      category: NodeCategory.EVENTS,
      icon: 'stop',
      color: '#f5222d',
      tags: ['事件', '生命周期', '结束', '批次1']
    });

    this.registerNode({
      type: 'core/events/onPause',
      label: '暂停事件',
      description: '当视觉脚本暂停时触发',
      category: NodeCategory.EVENTS,
      icon: 'pause',
      color: '#fa8c16',
      tags: ['事件', '生命周期', '暂停', '批次1']
    });

    // 添加时间节点
    this.registerNode({
      type: 'time/delay',
      label: '延迟',
      description: '延迟指定时间后执行',
      category: NodeCategory.TIME,
      icon: 'clock',
      color: '#eb2f96',
      tags: ['时间', '延迟', '控制', '批次1']
    });

    this.registerNode({
      type: 'time/timer',
      label: '计时器',
      description: '创建定时器进行周期性执行',
      category: NodeCategory.TIME,
      icon: 'timer',
      color: '#eb2f96',
      tags: ['时间', '计时器', '周期', '批次1']
    });

    console.log('第1批次核心基础节点注册完成：50个节点（001-050）');
  }

  /**
   * 初始化第2批次节点：扩展功能节点 (051-085)
   */
  private initializeBatch2Nodes(): void {
    // 时间节点 (051-052)
    this.registerNode({
      type: 'time/delay',
      label: '延迟',
      description: '延迟指定时间后执行',
      category: NodeCategory.TIME,
      icon: 'clock',
      color: '#eb2f96',
      tags: ['时间', '延迟', '控制', '批次2']
    });

    this.registerNode({
      type: 'time/timer',
      label: '计时器',
      description: '创建定时器进行周期性执行',
      category: NodeCategory.TIME,
      icon: 'timer',
      color: '#eb2f96',
      tags: ['时间', '计时器', '周期', '批次2']
    });

    // 动画节点 (053-056)
    this.registerNode({
      type: 'animation/playAnimation',
      label: '播放动画',
      description: '播放实体动画',
      category: NodeCategory.ANIMATION,
      icon: 'play',
      color: '#4CAF50',
      tags: ['动画', '播放', '控制', '批次2']
    });

    this.registerNode({
      type: 'animation/stopAnimation',
      label: '停止动画',
      description: '停止实体动画',
      category: NodeCategory.ANIMATION,
      icon: 'stop',
      color: '#f5222d',
      tags: ['动画', '停止', '控制', '批次2']
    });

    this.registerNode({
      type: 'animation/setAnimationSpeed',
      label: '设置动画速度',
      description: '设置动画播放速度',
      category: NodeCategory.ANIMATION,
      icon: 'speed',
      color: '#fa8c16',
      tags: ['动画', '速度', '控制', '批次2']
    });

    this.registerNode({
      type: 'animation/getAnimationState',
      label: '获取动画状态',
      description: '获取动画当前状态',
      category: NodeCategory.ANIMATION,
      icon: 'info',
      color: '#1890ff',
      tags: ['动画', '状态', '查询', '批次2']
    });

    // 输入节点 (057-059)
    this.registerNode({
      type: 'input/keyboard',
      label: '键盘输入',
      description: '检测键盘输入状态',
      category: NodeCategory.INPUT,
      icon: 'keyboard',
      color: '#722ed1',
      tags: ['输入', '键盘', '交互', '批次2']
    });

    this.registerNode({
      type: 'input/mouse',
      label: '鼠标输入',
      description: '检测鼠标输入状态',
      category: NodeCategory.INPUT,
      icon: 'mouse',
      color: '#722ed1',
      tags: ['输入', '鼠标', '交互', '批次2']
    });

    this.registerNode({
      type: 'input/gamepad',
      label: '游戏手柄输入',
      description: '检测游戏手柄输入状态',
      category: NodeCategory.INPUT,
      icon: 'gamepad',
      color: '#722ed1',
      tags: ['输入', '手柄', '交互', '批次2']
    });

    // 音频节点 (060-064)
    this.registerNode({
      type: 'audio/playAudio',
      label: '播放音频',
      description: '播放音频文件',
      category: NodeCategory.AUDIO,
      icon: 'play',
      color: '#52c41a',
      tags: ['音频', '播放', '媒体', '批次2']
    });

    this.registerNode({
      type: 'audio/stopAudio',
      label: '停止音频',
      description: '停止音频播放',
      category: NodeCategory.AUDIO,
      icon: 'stop',
      color: '#f5222d',
      tags: ['音频', '停止', '媒体', '批次2']
    });

    this.registerNode({
      type: 'audio/setVolume',
      label: '设置音量',
      description: '设置音频音量',
      category: NodeCategory.AUDIO,
      icon: 'volume',
      color: '#fa8c16',
      tags: ['音频', '音量', '控制', '批次2']
    });

    this.registerNode({
      type: 'audio/analyzer',
      label: '音频分析',
      description: '分析音频频谱数据',
      category: NodeCategory.AUDIO,
      icon: 'analyzer',
      color: '#1890ff',
      tags: ['音频', '分析', '频谱', '批次2']
    });

    this.registerNode({
      type: 'audio/audio3D',
      label: '3D音频',
      description: '创建3D空间音频',
      category: NodeCategory.AUDIO,
      icon: '3d-audio',
      color: '#13c2c2',
      tags: ['音频', '3D', '空间', '批次2']
    });

    // 调试节点 (065-069)
    this.registerNode({
      type: 'debug/breakpoint',
      label: '断点',
      description: '设置调试断点',
      category: NodeCategory.DEBUG,
      icon: 'breakpoint',
      color: '#722ed1',
      tags: ['调试', '断点', '开发', '批次2']
    });

    this.registerNode({
      type: 'debug/log',
      label: '日志',
      description: '输出调试日志',
      category: NodeCategory.DEBUG,
      icon: 'log',
      color: '#722ed1',
      tags: ['调试', '日志', '输出', '批次2']
    });

    this.registerNode({
      type: 'debug/performanceTimer',
      label: '性能计时',
      description: '测量代码执行时间',
      category: NodeCategory.DEBUG,
      icon: 'timer',
      color: '#722ed1',
      tags: ['调试', '性能', '计时', '批次2']
    });

    this.registerNode({
      type: 'debug/variableWatch',
      label: '变量监视',
      description: '监视变量值变化',
      category: NodeCategory.DEBUG,
      icon: 'watch',
      color: '#722ed1',
      tags: ['调试', '变量', '监视', '批次2']
    });

    this.registerNode({
      type: 'debug/assert',
      label: '断言',
      description: '验证条件是否为真',
      category: NodeCategory.DEBUG,
      icon: 'assert',
      color: '#722ed1',
      tags: ['调试', '断言', '验证', '批次2']
    });

    // 网络安全节点 (070-074)
    this.registerNode({
      type: 'network/security/encryptData',
      label: '数据加密',
      description: '加密数据',
      category: NodeCategory.NETWORK,
      icon: 'encrypt',
      color: '#fa8c16',
      tags: ['网络', '安全', '加密', '批次2']
    });

    this.registerNode({
      type: 'network/security/decryptData',
      label: '数据解密',
      description: '解密数据',
      category: NodeCategory.NETWORK,
      icon: 'decrypt',
      color: '#fa8c16',
      tags: ['网络', '安全', '解密', '批次2']
    });

    this.registerNode({
      type: 'network/security/hashData',
      label: '数据哈希',
      description: '计算数据哈希值',
      category: NodeCategory.NETWORK,
      icon: 'hash',
      color: '#fa8c16',
      tags: ['网络', '安全', '哈希', '批次2']
    });

    this.registerNode({
      type: 'network/security/authenticateUser',
      label: '用户认证',
      description: '验证用户身份',
      category: NodeCategory.NETWORK,
      icon: 'auth',
      color: '#fa8c16',
      tags: ['网络', '安全', '认证', '批次2']
    });

    this.registerNode({
      type: 'network/security/validateSession',
      label: '验证会话',
      description: '验证用户会话',
      category: NodeCategory.NETWORK,
      icon: 'session',
      color: '#fa8c16',
      tags: ['网络', '安全', '会话', '批次2']
    });

    // WebRTC节点 (075-078)
    this.registerNode({
      type: 'network/webrtc/createConnection',
      label: '创建WebRTC连接',
      description: '创建WebRTC连接',
      category: NodeCategory.NETWORK,
      icon: 'webrtc',
      color: '#13c2c2',
      tags: ['网络', 'WebRTC', '连接', '批次2']
    });

    this.registerNode({
      type: 'network/webrtc/sendDataChannelMessage',
      label: '发送数据通道消息',
      description: '通过WebRTC数据通道发送消息',
      category: NodeCategory.NETWORK,
      icon: 'send',
      color: '#13c2c2',
      tags: ['网络', 'WebRTC', '消息', '批次2']
    });

    this.registerNode({
      type: 'network/webrtc/createDataChannel',
      label: '创建数据通道',
      description: '创建WebRTC数据通道',
      category: NodeCategory.NETWORK,
      icon: 'channel',
      color: '#13c2c2',
      tags: ['网络', 'WebRTC', '通道', '批次2']
    });

    this.registerNode({
      type: 'network/webrtc/closeConnection',
      label: '关闭WebRTC连接',
      description: '关闭WebRTC连接',
      category: NodeCategory.NETWORK,
      icon: 'close',
      color: '#13c2c2',
      tags: ['网络', 'WebRTC', '关闭', '批次2']
    });

    // AI情感节点 (079-080)
    this.registerNode({
      type: 'ai/emotion/analyze',
      label: '情感分析',
      description: '分析文本或语音的情感',
      category: NodeCategory.AI,
      icon: 'emotion',
      color: '#eb2f96',
      tags: ['AI', '情感', '分析', '批次2']
    });

    this.registerNode({
      type: 'ai/emotion/driveAnimation',
      label: '情感驱动动画',
      description: '根据情感数据驱动动画',
      category: NodeCategory.AI,
      icon: 'animation',
      color: '#eb2f96',
      tags: ['AI', '情感', '动画', '批次2']
    });

    // AI自然语言处理节点 (081-084)
    this.registerNode({
      type: 'ai/nlp/classifyText',
      label: '文本分类',
      description: '对文本进行分类',
      category: NodeCategory.AI,
      icon: 'classify',
      color: '#eb2f96',
      tags: ['AI', 'NLP', '分类', '批次2']
    });

    this.registerNode({
      type: 'ai/nlp/recognizeEntities',
      label: '命名实体识别',
      description: '识别文本中的命名实体',
      category: NodeCategory.AI,
      icon: 'entity',
      color: '#eb2f96',
      tags: ['AI', 'NLP', '实体', '批次2']
    });

    this.registerNode({
      type: 'ai/nlp/analyzeSentiment',
      label: '情感分析',
      description: '分析文本情感倾向',
      category: NodeCategory.AI,
      icon: 'sentiment',
      color: '#eb2f96',
      tags: ['AI', 'NLP', '情感', '批次2']
    });

    this.registerNode({
      type: 'ai/nlp/extractKeywords',
      label: '关键词提取',
      description: '提取文本关键词',
      category: NodeCategory.AI,
      icon: 'keywords',
      color: '#eb2f96',
      tags: ['AI', 'NLP', '关键词', '批次2']
    });

    // 网络协议节点 (085-087)
    this.registerNode({
      type: 'network/protocol/udpSend',
      label: 'UDP发送',
      description: '通过UDP协议发送数据',
      category: NodeCategory.NETWORK,
      icon: 'udp',
      color: '#fa8c16',
      tags: ['网络', '协议', 'UDP', '批次2']
    });

    this.registerNode({
      type: 'network/protocol/httpRequest',
      label: 'HTTP请求',
      description: '发送HTTP请求',
      category: NodeCategory.NETWORK,
      icon: 'http',
      color: '#fa8c16',
      tags: ['网络', '协议', 'HTTP', '批次2']
    });

    this.registerNode({
      type: 'network/protocol/tcpConnect',
      label: 'TCP连接',
      description: '建立TCP连接',
      category: NodeCategory.NETWORK,
      icon: 'tcp',
      color: '#fa8c16',
      tags: ['网络', '协议', 'TCP', '批次2']
    });

    console.log('第2批次扩展功能节点注册完成：35个节点（051-087）');
  }

  /**
   * 初始化第3批次节点：数据处理节点 (088-100)
   */
  private initializeBatch3Nodes(): void {
    // 字符串操作节点 (088-095)
    this.registerNode({
      type: 'string/concat',
      label: '字符串连接',
      description: '连接多个字符串',
      category: NodeCategory.STRING,
      icon: 'link',
      color: '#fa541c',
      tags: ['字符串', '连接', '操作', '批次3']
    });

    this.registerNode({
      type: 'string/substring',
      label: '子字符串',
      description: '提取字符串的子串',
      category: NodeCategory.STRING,
      icon: 'scissor',
      color: '#fa541c',
      tags: ['字符串', '子串', '提取', '批次3']
    });

    this.registerNode({
      type: 'string/replace',
      label: '字符串替换',
      description: '替换字符串中的内容',
      category: NodeCategory.STRING,
      icon: 'replace',
      color: '#fa541c',
      tags: ['字符串', '替换', '操作', '批次3']
    });

    this.registerNode({
      type: 'string/split',
      label: '字符串分割',
      description: '分割字符串为数组',
      category: NodeCategory.STRING,
      icon: 'split',
      color: '#fa541c',
      tags: ['字符串', '分割', '操作', '批次3']
    });

    this.registerNode({
      type: 'string/length',
      label: '字符串长度',
      description: '获取字符串长度',
      category: NodeCategory.STRING,
      icon: 'length',
      color: '#fa541c',
      tags: ['字符串', '长度', '查询', '批次3']
    });

    this.registerNode({
      type: 'string/toUpperCase',
      label: '转大写',
      description: '将字符串转为大写',
      category: NodeCategory.STRING,
      icon: 'uppercase',
      color: '#fa541c',
      tags: ['字符串', '大写', '转换', '批次3']
    });

    this.registerNode({
      type: 'string/toLowerCase',
      label: '转小写',
      description: '将字符串转为小写',
      category: NodeCategory.STRING,
      icon: 'lowercase',
      color: '#fa541c',
      tags: ['字符串', '小写', '转换', '批次3']
    });

    this.registerNode({
      type: 'string/trim',
      label: '去除空格',
      description: '去除字符串首尾空格',
      category: NodeCategory.STRING,
      icon: 'trim',
      color: '#fa541c',
      tags: ['字符串', '空格', '清理', '批次3']
    });

    // 数组操作节点 (096-103)
    this.registerNode({
      type: 'array/push',
      label: '数组添加',
      description: '向数组末尾添加元素',
      category: NodeCategory.ARRAY,
      icon: 'push',
      color: '#52c41a',
      tags: ['数组', '添加', '操作', '批次3']
    });

    this.registerNode({
      type: 'array/pop',
      label: '数组弹出',
      description: '从数组末尾移除元素',
      category: NodeCategory.ARRAY,
      icon: 'pop',
      color: '#52c41a',
      tags: ['数组', '移除', '操作', '批次3']
    });

    this.registerNode({
      type: 'array/length',
      label: '数组长度',
      description: '获取数组长度',
      category: NodeCategory.ARRAY,
      icon: 'length',
      color: '#52c41a',
      tags: ['数组', '长度', '查询', '批次3']
    });

    this.registerNode({
      type: 'array/get',
      label: '获取元素',
      description: '获取数组指定位置的元素',
      category: NodeCategory.ARRAY,
      icon: 'get',
      color: '#52c41a',
      tags: ['数组', '获取', '访问', '批次3']
    });

    this.registerNode({
      type: 'array/set',
      label: '设置元素',
      description: '设置数组指定位置的元素',
      category: NodeCategory.ARRAY,
      icon: 'set',
      color: '#52c41a',
      tags: ['数组', '设置', '修改', '批次3']
    });

    console.log('第3批次数据处理节点注册完成：13个节点（088-100）');
  }

  /**
   * 初始化第4批次节点：数据处理和渲染基础节点 (101-120)
   */
  private initializeBatch4Nodes(): void {
    // 对象操作节点 (104-110)
    this.registerNode({
      type: 'object/getProperty',
      label: '获取属性',
      description: '获取对象的属性值',
      category: NodeCategory.OBJECT,
      icon: 'property',
      color: '#722ed1',
      tags: ['对象', '属性', '获取', '批次4']
    });

    this.registerNode({
      type: 'object/setProperty',
      label: '设置属性',
      description: '设置对象的属性值',
      category: NodeCategory.OBJECT,
      icon: 'property-set',
      color: '#722ed1',
      tags: ['对象', '属性', '设置', '批次4']
    });

    this.registerNode({
      type: 'object/hasProperty',
      label: '检查属性',
      description: '检查对象是否具有指定属性',
      category: NodeCategory.OBJECT,
      icon: 'property-check',
      color: '#722ed1',
      tags: ['对象', '属性', '检查', '批次4']
    });

    this.registerNode({
      type: 'object/keys',
      label: '获取键列表',
      description: '获取对象的所有键',
      category: NodeCategory.OBJECT,
      icon: 'keys',
      color: '#722ed1',
      tags: ['对象', '键', '列表', '批次4']
    });

    this.registerNode({
      type: 'object/values',
      label: '获取值列表',
      description: '获取对象的所有值',
      category: NodeCategory.OBJECT,
      icon: 'values',
      color: '#722ed1',
      tags: ['对象', '值', '列表', '批次4']
    });

    this.registerNode({
      type: 'object/merge',
      label: '对象合并',
      description: '合并多个对象',
      category: NodeCategory.OBJECT,
      icon: 'merge',
      color: '#722ed1',
      tags: ['对象', '合并', '操作', '批次4']
    });

    this.registerNode({
      type: 'object/clone',
      label: '对象克隆',
      description: '克隆对象',
      category: NodeCategory.OBJECT,
      icon: 'clone',
      color: '#722ed1',
      tags: ['对象', '克隆', '复制', '批次4']
    });

    // 变量操作节点 (111-117)
    this.registerNode({
      type: 'variable/get',
      label: '获取变量',
      description: '获取变量的值',
      category: NodeCategory.VARIABLE,
      icon: 'variable',
      color: '#fa541c',
      tags: ['变量', '获取', '值', '批次4']
    });

    this.registerNode({
      type: 'variable/set',
      label: '设置变量',
      description: '设置变量的值',
      category: NodeCategory.VARIABLE,
      icon: 'variable-set',
      color: '#fa541c',
      tags: ['变量', '设置', '值', '批次4']
    });

    this.registerNode({
      type: 'variable/increment',
      label: '变量递增',
      description: '将变量值递增',
      category: NodeCategory.VARIABLE,
      icon: 'increment',
      color: '#fa541c',
      tags: ['变量', '递增', '操作', '批次4']
    });

    this.registerNode({
      type: 'variable/decrement',
      label: '变量递减',
      description: '将变量值递减',
      category: NodeCategory.VARIABLE,
      icon: 'decrement',
      color: '#fa541c',
      tags: ['变量', '递减', '操作', '批次4']
    });

    this.registerNode({
      type: 'variable/exists',
      label: '变量存在',
      description: '检查变量是否存在',
      category: NodeCategory.VARIABLE,
      icon: 'exists',
      color: '#fa541c',
      tags: ['变量', '存在', '检查', '批次4']
    });

    this.registerNode({
      type: 'variable/delete',
      label: '删除变量',
      description: '删除指定变量',
      category: NodeCategory.VARIABLE,
      icon: 'delete',
      color: '#fa541c',
      tags: ['变量', '删除', '操作', '批次4']
    });

    this.registerNode({
      type: 'variable/type',
      label: '变量类型',
      description: '获取变量的数据类型',
      category: NodeCategory.VARIABLE,
      icon: 'type',
      color: '#fa541c',
      tags: ['变量', '类型', '检查', '批次4']
    });

    // 渲染相机节点 (118-120)
    this.registerNode({
      type: 'rendering/camera/createPerspectiveCamera',
      label: '创建透视相机',
      description: '创建透视投影相机',
      category: NodeCategory.RENDERING,
      icon: 'camera',
      color: '#FF5722',
      tags: ['渲染', '相机', '透视', '3D']
    });

    this.registerNode({
      type: 'rendering/camera/createOrthographicCamera',
      label: '创建正交相机',
      description: '创建正交投影相机',
      category: NodeCategory.RENDERING,
      icon: 'camera',
      color: '#FF5722',
      tags: ['渲染', '相机', '正交', '2D']
    });

    this.registerNode({
      type: 'rendering/camera/setCameraPosition',
      label: '设置相机位置',
      description: '设置相机在3D空间的位置',
      category: NodeCategory.RENDERING,
      icon: 'camera',
      color: '#FF5722',
      tags: ['渲染', '相机', '位置', '变换']
    });

    console.log('第4批次数据处理和渲染基础节点注册完成：20个节点（101-120）');
  }

  /**
   * 注册节点
   * @param nodeInfo 节点信息
   */
  public registerNode(nodeInfo: NodeInfo): void {
    // 检查是否已存在
    if (this.registeredNodes.has(nodeInfo.type)) {
      console.warn(`节点类型已存在: ${nodeInfo.type}`);
      return;
    }

    // 注册节点
    this.registeredNodes.set(nodeInfo.type, nodeInfo);

    // 添加到分类映射
    if (!this.nodesByCategory.has(nodeInfo.category)) {
      this.nodesByCategory.set(nodeInfo.category, []);
    }
    this.nodesByCategory.get(nodeInfo.category)!.push(nodeInfo);

    // 添加到标签映射
    for (const tag of nodeInfo.tags) {
      if (!this.nodesByTag.has(tag)) {
        this.nodesByTag.set(tag, []);
      }
      this.nodesByTag.get(tag)!.push(nodeInfo);
    }

    // 触发注册事件
    this.emit('nodeRegistered', nodeInfo);
  }

  /**
   * 获取所有节点
   */
  public getAllNodes(): NodeInfo[] {
    return Array.from(this.registeredNodes.values());
  }

  /**
   * 根据类型获取节点
   * @param type 节点类型
   */
  public getNode(type: string): NodeInfo | undefined {
    return this.registeredNodes.get(type);
  }

  /**
   * 根据分类获取节点
   * @param category 节点分类
   */
  public getNodesByCategory(category: NodeCategory): NodeInfo[] {
    return this.nodesByCategory.get(category) || [];
  }

  /**
   * 根据标签获取节点
   * @param tag 标签
   */
  public getNodesByTag(tag: string): NodeInfo[] {
    return this.nodesByTag.get(tag) || [];
  }

  /**
   * 搜索节点
   * @param query 搜索关键词
   */
  public searchNodes(query: string): NodeInfo[] {
    const lowerQuery = query.toLowerCase();
    return this.getAllNodes().filter(node => 
      node.label.toLowerCase().includes(lowerQuery) ||
      node.description.toLowerCase().includes(lowerQuery) ||
      node.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }

  /**
   * 获取所有分类
   */
  public getAllCategories(): NodeCategory[] {
    return Array.from(this.nodesByCategory.keys());
  }

  /**
   * 获取所有标签
   */
  public getAllTags(): string[] {
    return Array.from(this.nodesByTag.keys());
  }

  /**
   * 注销节点
   * @param type 节点类型
   */
  public unregisterNode(type: string): boolean {
    const nodeInfo = this.registeredNodes.get(type);
    if (!nodeInfo) {
      return false;
    }

    // 从主映射中移除
    this.registeredNodes.delete(type);

    // 从分类映射中移除
    const categoryNodes = this.nodesByCategory.get(nodeInfo.category);
    if (categoryNodes) {
      const index = categoryNodes.findIndex(n => n.type === type);
      if (index !== -1) {
        categoryNodes.splice(index, 1);
      }
    }

    // 从标签映射中移除
    for (const tag of nodeInfo.tags) {
      const tagNodes = this.nodesByTag.get(tag);
      if (tagNodes) {
        const index = tagNodes.findIndex(n => n.type === type);
        if (index !== -1) {
          tagNodes.splice(index, 1);
        }
      }
    }

    // 触发注销事件
    this.emit('nodeUnregistered', nodeInfo);

    return true;
  }

  /**
   * 清空所有节点
   */
  public clear(): void {
    this.registeredNodes.clear();
    this.nodesByCategory.clear();
    this.nodesByTag.clear();
    this.emit('cleared');
  }

  /**
   * 获取节点统计信息
   */
  public getStats(): {
    totalNodes: number;
    categoryCounts: Record<string, number>;
    tagCounts: Record<string, number>;
  } {
    const categoryCounts: Record<string, number> = {};
    const tagCounts: Record<string, number> = {};

    for (const [category, nodes] of this.nodesByCategory.entries()) {
      categoryCounts[category] = nodes.length;
    }

    for (const [tag, nodes] of this.nodesByTag.entries()) {
      tagCounts[tag] = nodes.length;
    }

    return {
      totalNodes: this.registeredNodes.size,
      categoryCounts,
      tagCounts
    };
  }

  /**
   * 获取节点统计信息（别名方法）
   */
  public getNodeStatistics(): {
    totalNodes: number;
    categoryCounts: Record<string, number>;
    tagCounts: Record<string, number>;
  } {
    return this.getStats();
  }

  /**
   * 初始化第5批次节点：渲染系统核心（节点121-140）
   */
  private initializeBatch5Nodes(): void {
    // 相机控制节点
    this.registerNode({
      type: 'rendering/camera/setCameraTarget',
      label: '设置相机目标',
      description: '设置相机观察目标点',
      category: NodeCategory.RENDERING,
      icon: 'camera-target',
      color: '#FF5722',
      tags: ['渲染', '相机', '目标']
    });

    this.registerNode({
      type: 'rendering/camera/setCameraFOV',
      label: '设置相机视野',
      description: '设置相机视野角度',
      category: NodeCategory.RENDERING,
      icon: 'camera-fov',
      color: '#FF5722',
      tags: ['渲染', '相机', '视野']
    });

    // 光照系统节点
    this.registerNode({
      type: 'rendering/light/createDirectionalLight',
      label: '创建方向光',
      description: '创建平行光源',
      category: NodeCategory.RENDERING,
      icon: 'directional-light',
      color: '#FFC107',
      tags: ['渲染', '光照', '方向光']
    });

    this.registerNode({
      type: 'rendering/light/createPointLight',
      label: '创建点光源',
      description: '创建点状光源',
      category: NodeCategory.RENDERING,
      icon: 'point-light',
      color: '#FFC107',
      tags: ['渲染', '光照', '点光源']
    });

    this.registerNode({
      type: 'rendering/light/createSpotLight',
      label: '创建聚光灯',
      description: '创建锥形光源',
      category: NodeCategory.RENDERING,
      icon: 'spot-light',
      color: '#FFC107',
      tags: ['渲染', '光照', '聚光灯']
    });

    this.registerNode({
      type: 'rendering/light/createAmbientLight',
      label: '创建环境光',
      description: '创建全局环境光',
      category: NodeCategory.RENDERING,
      icon: 'ambient-light',
      color: '#FFC107',
      tags: ['渲染', '光照', '环境光']
    });

    this.registerNode({
      type: 'rendering/light/setLightColor',
      label: '设置光源颜色',
      description: '设置光源的颜色属性',
      category: NodeCategory.RENDERING,
      icon: 'light-color',
      color: '#FFC107',
      tags: ['渲染', '光照', '颜色']
    });

    this.registerNode({
      type: 'rendering/light/setLightIntensity',
      label: '设置光源强度',
      description: '设置光源的亮度强度',
      category: NodeCategory.RENDERING,
      icon: 'light-intensity',
      color: '#FFC107',
      tags: ['渲染', '光照', '强度']
    });

    // 阴影系统节点
    this.registerNode({
      type: 'rendering/shadow/enableShadows',
      label: '启用阴影',
      description: '启用实时阴影渲染',
      category: NodeCategory.RENDERING,
      icon: 'shadow',
      color: '#9C27B0',
      tags: ['渲染', '阴影', '启用']
    });

    this.registerNode({
      type: 'rendering/shadow/setShadowMapSize',
      label: '设置阴影贴图大小',
      description: '设置阴影质量',
      category: NodeCategory.RENDERING,
      icon: 'shadow-map',
      color: '#9C27B0',
      tags: ['渲染', '阴影', '质量']
    });

    // 材质系统节点
    this.registerNode({
      type: 'rendering/material/createBasicMaterial',
      label: '创建基础材质',
      description: '创建简单材质',
      category: NodeCategory.RENDERING,
      icon: 'material-basic',
      color: '#607D8B',
      tags: ['渲染', '材质', '基础']
    });

    this.registerNode({
      type: 'rendering/material/createStandardMaterial',
      label: '创建标准材质',
      description: '创建PBR材质',
      category: NodeCategory.RENDERING,
      icon: 'material-standard',
      color: '#607D8B',
      tags: ['渲染', '材质', 'PBR']
    });

    this.registerNode({
      type: 'rendering/material/createPhysicalMaterial',
      label: '创建物理材质',
      description: '创建物理基础材质',
      category: NodeCategory.RENDERING,
      icon: 'material-physical',
      color: '#607D8B',
      tags: ['渲染', '材质', '物理']
    });

    this.registerNode({
      type: 'rendering/material/setMaterialColor',
      label: '设置材质颜色',
      description: '设置材质的基础颜色',
      category: NodeCategory.RENDERING,
      icon: 'material-color',
      color: '#607D8B',
      tags: ['渲染', '材质', '颜色']
    });

    this.registerNode({
      type: 'rendering/material/setMaterialTexture',
      label: '设置材质纹理',
      description: '设置材质的纹理贴图',
      category: NodeCategory.RENDERING,
      icon: 'material-texture',
      color: '#607D8B',
      tags: ['渲染', '材质', '纹理']
    });

    this.registerNode({
      type: 'rendering/material/setMaterialOpacity',
      label: '设置材质透明度',
      description: '设置材质的透明程度',
      category: NodeCategory.RENDERING,
      icon: 'material-opacity',
      color: '#607D8B',
      tags: ['渲染', '材质', '透明度']
    });

    // 后处理节点
    this.registerNode({
      type: 'rendering/postprocess/enableFXAA',
      label: '启用抗锯齿',
      description: '启用FXAA抗锯齿',
      category: NodeCategory.RENDERING,
      icon: 'anti-aliasing',
      color: '#795548',
      tags: ['渲染', '后处理', '抗锯齿']
    });

    this.registerNode({
      type: 'rendering/postprocess/enableSSAO',
      label: '启用环境光遮蔽',
      description: '启用屏幕空间环境光遮蔽',
      category: NodeCategory.RENDERING,
      icon: 'ssao',
      color: '#795548',
      tags: ['渲染', '后处理', 'SSAO']
    });

    this.registerNode({
      type: 'rendering/postprocess/enableBloom',
      label: '启用辉光效果',
      description: '启用Bloom后处理',
      category: NodeCategory.RENDERING,
      icon: 'bloom',
      color: '#795548',
      tags: ['渲染', '后处理', '辉光']
    });

    // LOD系统节点
    this.registerNode({
      type: 'rendering/lod/setLODLevel',
      label: '设置LOD级别',
      description: '设置细节层次级别',
      category: NodeCategory.RENDERING,
      icon: 'lod',
      color: '#FF9800',
      tags: ['渲染', 'LOD', '优化']
    });

    // 物理刚体节点
    this.registerNode({
      type: 'physics/rigidbody/createRigidBody',
      label: '创建刚体',
      description: '创建物理刚体对象',
      category: NodeCategory.PHYSICS,
      icon: 'rigid-body',
      color: '#E91E63',
      tags: ['物理', '刚体', '创建']
    });

    this.registerNode({
      type: 'physics/rigidbody/setMass',
      label: '设置质量',
      description: '设置刚体质量',
      category: NodeCategory.PHYSICS,
      icon: 'mass',
      color: '#E91E63',
      tags: ['物理', '刚体', '质量']
    });

    this.registerNode({
      type: 'physics/rigidbody/setFriction',
      label: '设置摩擦力',
      description: '设置表面摩擦系数',
      category: NodeCategory.PHYSICS,
      icon: 'friction',
      color: '#E91E63',
      tags: ['物理', '刚体', '摩擦力']
    });

    this.registerNode({
      type: 'physics/rigidbody/setRestitution',
      label: '设置弹性',
      description: '设置碰撞弹性系数',
      category: NodeCategory.PHYSICS,
      icon: 'restitution',
      color: '#E91E63',
      tags: ['物理', '刚体', '弹性']
    });

    console.log('第5批次渲染系统核心节点注册完成：20个节点（121-140）');
  }

  /**
   * 初始化第6批次节点：高级物理系统节点 (141-150)
   */
  private initializeBatch6Nodes(): void {
    // 高级物理系统节点 (141-150)
    this.registerNode({
      type: 'physics/advanced/createSoftBody',
      label: '创建软体',
      description: '创建可变形软体物理对象',
      category: NodeCategory.PHYSICS,
      icon: 'soft-body',
      color: '#E91E63',
      tags: ['物理', '软体', '高级', '批次6']
    });

    this.registerNode({
      type: 'physics/advanced/createFluid',
      label: '创建流体',
      description: '创建流体物理系统',
      category: NodeCategory.PHYSICS,
      icon: 'fluid',
      color: '#E91E63',
      tags: ['物理', '流体', '高级', '批次6']
    });

    this.registerNode({
      type: 'physics/advanced/createCloth',
      label: '创建布料',
      description: '创建布料物理模拟',
      category: NodeCategory.PHYSICS,
      icon: 'cloth',
      color: '#E91E63',
      tags: ['物理', '布料', '高级', '批次6']
    });

    this.registerNode({
      type: 'physics/advanced/createParticleSystem',
      label: '创建粒子系统',
      description: '创建物理粒子系统',
      category: NodeCategory.PHYSICS,
      icon: 'particles',
      color: '#E91E63',
      tags: ['物理', '粒子', '高级', '批次6']
    });

    this.registerNode({
      type: 'physics/advanced/setGravity',
      label: '设置重力',
      description: '设置物理世界重力',
      category: NodeCategory.PHYSICS,
      icon: 'gravity',
      color: '#E91E63',
      tags: ['物理', '重力', '高级', '批次6']
    });

    this.registerNode({
      type: 'physics/advanced/createJoint',
      label: '创建关节',
      description: '创建物理关节连接',
      category: NodeCategory.PHYSICS,
      icon: 'joint',
      color: '#E91E63',
      tags: ['物理', '关节', '高级', '批次6']
    });

    this.registerNode({
      type: 'physics/advanced/setDamping',
      label: '设置阻尼',
      description: '设置物理阻尼系数',
      category: NodeCategory.PHYSICS,
      icon: 'damping',
      color: '#E91E63',
      tags: ['物理', '阻尼', '高级', '批次6']
    });

    this.registerNode({
      type: 'physics/advanced/createConstraint',
      label: '创建约束',
      description: '创建物理约束',
      category: NodeCategory.PHYSICS,
      icon: 'constraint',
      color: '#E91E63',
      tags: ['物理', '约束', '高级', '批次6']
    });

    this.registerNode({
      type: 'physics/advanced/simulateWind',
      label: '模拟风力',
      description: '模拟风力效果',
      category: NodeCategory.PHYSICS,
      icon: 'wind',
      color: '#E91E63',
      tags: ['物理', '风力', '高级', '批次6']
    });

    this.registerNode({
      type: 'physics/advanced/createExplosion',
      label: '创建爆炸',
      description: '创建爆炸物理效果',
      category: NodeCategory.PHYSICS,
      icon: 'explosion',
      color: '#E91E63',
      tags: ['物理', '爆炸', '高级', '批次6']
    });

    console.log('第6批次高级物理系统节点注册完成：10个节点（141-150）');
  }

  /**
   * 初始化第7批次节点：动画系统扩展（节点181-210）
   */
  private initializeBatch7Nodes(): void {
    // 动画曲线节点（181-185）
    this.registerNode({
      type: 'animation/curve/evaluateCurve',
      label: '计算曲线值',
      description: '计算动画曲线在指定时间的值',
      category: NodeCategory.ANIMATION,
      icon: 'curve',
      color: '#4CAF50',
      tags: ['动画', '曲线', '计算']
    });

    this.registerNode({
      type: 'animation/state/createStateMachine',
      label: '创建状态机',
      description: '创建动画状态机',
      category: NodeCategory.ANIMATION,
      icon: 'state-machine',
      color: '#4CAF50',
      tags: ['动画', '状态机', '创建']
    });

    this.registerNode({
      type: 'animation/state/addState',
      label: '添加状态',
      description: '向状态机添加动画状态',
      category: NodeCategory.ANIMATION,
      icon: 'state-add',
      color: '#4CAF50',
      tags: ['动画', '状态', '添加']
    });

    this.registerNode({
      type: 'animation/state/addTransition',
      label: '添加过渡',
      description: '在状态间添加过渡条件',
      category: NodeCategory.ANIMATION,
      icon: 'transition',
      color: '#4CAF50',
      tags: ['动画', '状态', '过渡']
    });

    this.registerNode({
      type: 'animation/state/setCurrentState',
      label: '设置当前状态',
      description: '切换到指定动画状态',
      category: NodeCategory.ANIMATION,
      icon: 'state-current',
      color: '#4CAF50',
      tags: ['动画', '状态', '切换']
    });

    // 高级音频系统节点（186-200）
    this.registerNode({
      type: 'audio/source/create3DAudioSource',
      label: '创建3D音频源',
      description: '创建空间音频源',
      category: NodeCategory.AUDIO,
      icon: 'audio-3d',
      color: '#FF9800',
      tags: ['音频', '3D', '空间']
    });

    this.registerNode({
      type: 'audio/source/setAudioPosition',
      label: '设置音频位置',
      description: '设置3D音频源位置',
      category: NodeCategory.AUDIO,
      icon: 'audio-position',
      color: '#FF9800',
      tags: ['音频', '位置', '3D']
    });

    this.registerNode({
      type: 'audio/source/setAudioVelocity',
      label: '设置音频速度',
      description: '设置音频源移动速度',
      category: NodeCategory.AUDIO,
      icon: 'audio-velocity',
      color: '#FF9800',
      tags: ['音频', '速度', '多普勒']
    });

    this.registerNode({
      type: 'audio/listener/setListenerPosition',
      label: '设置听者位置',
      description: '设置音频听者位置',
      category: NodeCategory.AUDIO,
      icon: 'listener-position',
      color: '#FF9800',
      tags: ['音频', '听者', '位置']
    });

    this.registerNode({
      type: 'audio/listener/setListenerOrientation',
      label: '设置听者朝向',
      description: '设置音频听者朝向',
      category: NodeCategory.AUDIO,
      icon: 'listener-orientation',
      color: '#FF9800',
      tags: ['音频', '听者', '朝向']
    });

    this.registerNode({
      type: 'audio/effect/createReverb',
      label: '创建混响效果',
      description: '创建音频混响处理器',
      category: NodeCategory.AUDIO,
      icon: 'reverb',
      color: '#FF9800',
      tags: ['音频', '效果', '混响']
    });

    this.registerNode({
      type: 'audio/effect/createEcho',
      label: '创建回声效果',
      description: '创建音频回声处理器',
      category: NodeCategory.AUDIO,
      icon: 'echo',
      color: '#FF9800',
      tags: ['音频', '效果', '回声']
    });

    this.registerNode({
      type: 'audio/effect/createFilter',
      label: '创建滤波器',
      description: '创建音频滤波处理器',
      category: NodeCategory.AUDIO,
      icon: 'filter',
      color: '#FF9800',
      tags: ['音频', '效果', '滤波器']
    });

    this.registerNode({
      type: 'audio/analysis/createAnalyzer',
      label: '创建音频分析器',
      description: '创建音频频谱分析器',
      category: NodeCategory.AUDIO,
      icon: 'analyzer',
      color: '#FF9800',
      tags: ['音频', '分析', '频谱']
    });

    this.registerNode({
      type: 'audio/analysis/getFrequencyData',
      label: '获取频率数据',
      description: '获取音频频谱数据',
      category: NodeCategory.AUDIO,
      icon: 'frequency',
      color: '#FF9800',
      tags: ['音频', '分析', '频率']
    });

    this.registerNode({
      type: 'audio/analysis/getWaveformData',
      label: '获取波形数据',
      description: '获取音频波形数据',
      category: NodeCategory.AUDIO,
      icon: 'waveform',
      color: '#FF9800',
      tags: ['音频', '分析', '波形']
    });

    this.registerNode({
      type: 'audio/streaming/createAudioStream',
      label: '创建音频流',
      description: '创建实时音频流',
      category: NodeCategory.AUDIO,
      icon: 'stream',
      color: '#FF9800',
      tags: ['音频', '流媒体', '实时']
    });

    this.registerNode({
      type: 'audio/streaming/connectStream',
      label: '连接音频流',
      description: '连接到音频流源',
      category: NodeCategory.AUDIO,
      icon: 'connect',
      color: '#FF9800',
      tags: ['音频', '流媒体', '连接']
    });

    this.registerNode({
      type: 'audio/recording/startRecording',
      label: '开始录音',
      description: '开始音频录制',
      category: NodeCategory.AUDIO,
      icon: 'record-start',
      color: '#FF9800',
      tags: ['音频', '录制', '开始']
    });

    this.registerNode({
      type: 'audio/recording/stopRecording',
      label: '停止录音',
      description: '停止音频录制',
      category: NodeCategory.AUDIO,
      icon: 'record-stop',
      color: '#FF9800',
      tags: ['音频', '录制', '停止']
    });

    // 场景管理系统节点（201-210）
    this.registerNode({
      type: 'scene/management/createScene',
      label: '创建场景',
      description: '创建新的3D场景',
      category: NodeCategory.ENTITY,
      icon: 'scene',
      color: '#2196F3',
      tags: ['场景', '管理', '创建']
    });

    this.registerNode({
      type: 'scene/management/loadScene',
      label: '加载场景',
      description: '从文件加载场景',
      category: NodeCategory.ENTITY,
      icon: 'scene-load',
      color: '#2196F3',
      tags: ['场景', '管理', '加载']
    });

    this.registerNode({
      type: 'scene/management/saveScene',
      label: '保存场景',
      description: '保存场景到文件',
      category: NodeCategory.ENTITY,
      icon: 'scene-save',
      color: '#2196F3',
      tags: ['场景', '管理', '保存']
    });

    this.registerNode({
      type: 'scene/management/switchScene',
      label: '切换场景',
      description: '切换到指定场景',
      category: NodeCategory.ENTITY,
      icon: 'scene-switch',
      color: '#2196F3',
      tags: ['场景', '管理', '切换']
    });

    this.registerNode({
      type: 'scene/management/addToScene',
      label: '添加到场景',
      description: '将对象添加到场景',
      category: NodeCategory.ENTITY,
      icon: 'scene-add',
      color: '#2196F3',
      tags: ['场景', '管理', '添加']
    });

    this.registerNode({
      type: 'scene/management/removeFromScene',
      label: '从场景移除',
      description: '从场景移除对象',
      category: NodeCategory.ENTITY,
      icon: 'scene-remove',
      color: '#2196F3',
      tags: ['场景', '管理', '移除']
    });

    this.registerNode({
      type: 'scene/culling/enableFrustumCulling',
      label: '启用视锥体剔除',
      description: '启用视锥体剔除优化',
      category: NodeCategory.ENTITY,
      icon: 'frustum-culling',
      color: '#2196F3',
      tags: ['场景', '优化', '剔除']
    });

    this.registerNode({
      type: 'scene/culling/enableOcclusionCulling',
      label: '启用遮挡剔除',
      description: '启用遮挡剔除优化',
      category: NodeCategory.ENTITY,
      icon: 'occlusion-culling',
      color: '#2196F3',
      tags: ['场景', '优化', '遮挡']
    });

    this.registerNode({
      type: 'scene/optimization/enableBatching',
      label: '启用批处理',
      description: '启用渲染批处理',
      category: NodeCategory.ENTITY,
      icon: 'batching',
      color: '#2196F3',
      tags: ['场景', '优化', '批处理']
    });

    this.registerNode({
      type: 'scene/optimization/enableInstancing',
      label: '启用实例化',
      description: '启用实例化渲染',
      category: NodeCategory.ENTITY,
      icon: 'instancing',
      color: '#2196F3',
      tags: ['场景', '优化', '实例化']
    });

    console.log('第7批次动画系统扩展节点注册完成：30个节点');
  }

  /**
   * 初始化第8批次节点：音频与粒子系统（节点211-240）
   * 完整版本 - 包含所有30个节点
   */
  private initializeBatch8Nodes(): void {
    // 高级音频节点（211-215）- 场景环境音频
    this.registerNode({
      type: 'scene/skybox/setSkybox',
      label: '设置天空盒',
      description: '设置场景天空盒',
      category: NodeCategory.ENTITY,
      icon: 'skybox',
      color: '#87CEEB',
      tags: ['场景', '天空盒', '环境']
    });

    this.registerNode({
      type: 'scene/fog/enableFog',
      label: '启用雾效',
      description: '启用场景雾效果',
      category: NodeCategory.ENTITY,
      icon: 'fog',
      color: '#B0C4DE',
      tags: ['场景', '雾效', '视觉效果']
    });

    this.registerNode({
      type: 'scene/fog/setFogColor',
      label: '设置雾颜色',
      description: '设置雾的颜色',
      category: NodeCategory.ENTITY,
      icon: 'color-palette',
      color: '#B0C4DE',
      tags: ['场景', '雾效', '颜色']
    });

    this.registerNode({
      type: 'scene/fog/setFogDensity',
      label: '设置雾密度',
      description: '设置雾的浓度',
      category: NodeCategory.ENTITY,
      icon: 'density',
      color: '#B0C4DE',
      tags: ['场景', '雾效', '密度']
    });

    this.registerNode({
      type: 'scene/environment/setEnvironmentMap',
      label: '设置环境贴图',
      description: '设置IBL环境贴图',
      category: NodeCategory.ENTITY,
      icon: 'environment',
      color: '#32CD32',
      tags: ['场景', '环境', 'IBL']
    });

    // 粒子系统节点（216-230）
    this.registerNode({
      type: 'particles/system/createParticleSystem',
      label: '创建粒子系统',
      description: '创建粒子效果系统',
      category: NodeCategory.ENTITY,
      icon: 'particles',
      color: '#FF6347',
      tags: ['粒子', '特效', '系统']
    });

    this.registerNode({
      type: 'particles/emitter/createEmitter',
      label: '创建发射器',
      description: '创建粒子发射器',
      category: NodeCategory.ENTITY,
      icon: 'emitter',
      color: '#FF6347',
      tags: ['粒子', '发射器', '特效']
    });

    this.registerNode({
      type: 'particles/emitter/setEmissionRate',
      label: '设置发射速率',
      description: '设置粒子发射频率',
      category: NodeCategory.ENTITY,
      icon: 'rate',
      color: '#FF6347',
      tags: ['粒子', '发射', '速率']
    });

    this.registerNode({
      type: 'particles/emitter/setEmissionShape',
      label: '设置发射形状',
      description: '设置粒子发射形状',
      category: NodeCategory.ENTITY,
      icon: 'shape',
      color: '#FF6347',
      tags: ['粒子', '发射', '形状']
    });

    this.registerNode({
      type: 'particles/particle/setLifetime',
      label: '设置粒子寿命',
      description: '设置粒子存活时间',
      category: NodeCategory.ENTITY,
      icon: 'lifetime',
      color: '#FFA500',
      tags: ['粒子', '寿命', '时间']
    });

    this.registerNode({
      type: 'particles/particle/setVelocity',
      label: '设置粒子速度',
      description: '设置粒子初始速度',
      category: NodeCategory.ENTITY,
      icon: 'velocity',
      color: '#FFA500',
      tags: ['粒子', '速度', '运动']
    });

    this.registerNode({
      type: 'particles/particle/setSize',
      label: '设置粒子大小',
      description: '设置粒子尺寸',
      category: NodeCategory.ENTITY,
      icon: 'size',
      color: '#FFA500',
      tags: ['粒子', '大小', '尺寸']
    });

    this.registerNode({
      type: 'particles/particle/setColor',
      label: '设置粒子颜色',
      description: '设置粒子颜色',
      category: NodeCategory.ENTITY,
      icon: 'color',
      color: '#FFA500',
      tags: ['粒子', '颜色', '外观']
    });

    this.registerNode({
      type: 'particles/forces/addGravity',
      label: '添加重力',
      description: '为粒子添加重力影响',
      category: NodeCategory.ENTITY,
      icon: 'gravity',
      color: '#8A2BE2',
      tags: ['粒子', '重力', '物理']
    });

    this.registerNode({
      type: 'particles/forces/addWind',
      label: '添加风力',
      description: '为粒子添加风力影响',
      category: NodeCategory.ENTITY,
      icon: 'wind',
      color: '#8A2BE2',
      tags: ['粒子', '风力', '物理']
    });

    this.registerNode({
      type: 'particles/forces/addTurbulence',
      label: '添加湍流',
      description: '为粒子添加湍流效果',
      category: NodeCategory.ENTITY,
      icon: 'turbulence',
      color: '#8A2BE2',
      tags: ['粒子', '湍流', '物理']
    });

    this.registerNode({
      type: 'particles/collision/enableCollision',
      label: '启用粒子碰撞',
      description: '启用粒子与物体碰撞',
      category: NodeCategory.ENTITY,
      icon: 'collision',
      color: '#DC143C',
      tags: ['粒子', '碰撞', '物理']
    });

    this.registerNode({
      type: 'particles/material/setParticleMaterial',
      label: '设置粒子材质',
      description: '设置粒子渲染材质',
      category: NodeCategory.ENTITY,
      icon: 'material',
      color: '#4169E1',
      tags: ['粒子', '材质', '渲染']
    });

    this.registerNode({
      type: 'particles/animation/animateSize',
      label: '动画粒子大小',
      description: '创建粒子大小动画',
      category: NodeCategory.ANIMATION,
      icon: 'animate-size',
      color: '#FF1493',
      tags: ['粒子', '动画', '大小']
    });

    this.registerNode({
      type: 'particles/animation/animateColor',
      label: '动画粒子颜色',
      description: '创建粒子颜色动画',
      category: NodeCategory.ANIMATION,
      icon: 'animate-color',
      color: '#FF1493',
      tags: ['粒子', '动画', '颜色']
    });

    // 地形系统节点（231-237）
    this.registerNode({
      type: 'terrain/generation/createTerrain',
      label: '创建地形',
      description: '创建地形网格',
      category: NodeCategory.ENTITY,
      icon: 'terrain',
      color: '#8B4513',
      tags: ['地形', '生成', '网格']
    });

    this.registerNode({
      type: 'terrain/generation/generateHeightmap',
      label: '生成高度图',
      description: '生成地形高度图',
      category: NodeCategory.ENTITY,
      icon: 'heightmap',
      color: '#8B4513',
      tags: ['地形', '高度图', '生成']
    });

    this.registerNode({
      type: 'terrain/generation/applyNoise',
      label: '应用噪声',
      description: '为地形应用噪声纹理',
      category: NodeCategory.ENTITY,
      icon: 'noise',
      color: '#8B4513',
      tags: ['地形', '噪声', '生成']
    });

    this.registerNode({
      type: 'terrain/texture/setTerrainTexture',
      label: '设置地形纹理',
      description: '设置地形表面纹理',
      category: NodeCategory.ENTITY,
      icon: 'texture',
      color: '#8B4513',
      tags: ['地形', '纹理', '外观']
    });

    this.registerNode({
      type: 'terrain/texture/blendTextures',
      label: '混合纹理',
      description: '混合多个地形纹理',
      category: NodeCategory.ENTITY,
      icon: 'blend',
      color: '#8B4513',
      tags: ['地形', '纹理', '混合']
    });

    this.registerNode({
      type: 'terrain/lod/enableTerrainLOD',
      label: '启用地形LOD',
      description: '启用地形细节层次',
      category: NodeCategory.ENTITY,
      icon: 'lod',
      color: '#8B4513',
      tags: ['地形', 'LOD', '优化']
    });

    this.registerNode({
      type: 'terrain/collision/enableTerrainCollision',
      label: '启用地形碰撞',
      description: '启用地形物理碰撞',
      category: NodeCategory.ENTITY,
      icon: 'collision',
      color: '#8B4513',
      tags: ['地形', '碰撞', '物理']
    });

    // 水体系统节点（238-240）
    this.registerNode({
      type: 'water/system/createWaterSurface',
      label: '创建水面',
      description: '创建水体表面',
      category: NodeCategory.ENTITY,
      icon: 'water',
      color: '#006994',
      tags: ['水体', '创建', '表面']
    });

    this.registerNode({
      type: 'water/waves/addWaves',
      label: '添加波浪',
      description: '为水面添加波浪效果',
      category: NodeCategory.ENTITY,
      icon: 'waves',
      color: '#006994',
      tags: ['水体', '波浪', '效果']
    });

    this.registerNode({
      type: 'water/reflection/enableReflection',
      label: '启用水面反射',
      description: '启用水面反射效果',
      category: NodeCategory.ENTITY,
      icon: 'reflection',
      color: '#006994',
      tags: ['水体', '反射', '视觉效果']
    });

    console.log('第8批次音频与粒子系统节点注册完成：30个节点（211-240）');
  }
}

// 导出单例实例
export const nodeRegistryService = NodeRegistryService.getInstance();
